#!/usr/bin/env python3
"""
JSON Processor - Compact and validate JSON using json5

A simple, elegant processor that takes any JSON input and:
1. Leverages json5 to handle malformed JSON (trailing commas, etc.)
2. Outputs compact, valid JSON syntax
3. Provides both CLI and programmatic interfaces

Core principle: Use json5 parsing to solve both syntax validation
and compaction in a single, unified process.
"""

import json
import json5
import sys
import argparse
from pathlib import Path
from typing import Union, Dict, List, Optional


class JsonProcessor:
    """Unified JSON processor for compaction and syntax validation."""

    def __init__(self, compact: bool = True, indent: Optional[int] = None):
        """
        Initialize processor with formatting options.

        Args:
            compact: If True, output compact JSON. If False, use indent.
            indent: Number of spaces for indentation (ignored if compact=True)
        """
        self.compact = compact
        self.indent = indent if not compact else None

    def process(self, input_data: Union[str, Dict, List]) -> str:
        """
        Process JSON input to compact, valid JSON output.

        Args:
            input_data: JSON string, dict, or list to process

        Returns:
            Compact, valid JSON string

        Raises:
            ValueError: If input cannot be parsed as JSON/JSON5
        """
        try:
            # Parse using json5 to handle malformed JSON
            if isinstance(input_data, str):
                parsed_data = json5.loads(input_data)
            else:
                parsed_data = input_data

            # Serialize to compact, valid JSON
            if self.compact:
                return json.dumps(parsed_data, separators=(',', ':'), ensure_ascii=False)
            else:
                return json.dumps(parsed_data, indent=self.indent, ensure_ascii=False)

        except (ValueError, json.JSONDecodeError, TypeError) as e:
            raise ValueError(f"Invalid JSON input: {e}")

    def process_file(
        self,
        input_path: Union[str, Path],
        output_path: Optional[Union[str, Path]] = None
    ) -> str:
        """
        Process JSON file and optionally save to output file.

        Args:
            input_path: Path to input JSON file
            output_path: Optional path to save output (if None, returns string)

        Returns:
            Processed JSON string
        """
        input_path = Path(input_path)

        if not input_path.exists():
            raise FileNotFoundError(f"Input file not found: {input_path}")

        with open(input_path, 'r', encoding='utf-8') as f:
            input_content = f.read()

        processed_json = self.process(input_content)

        if output_path:
            output_path = Path(output_path)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(processed_json)

        return processed_json


def main():
    """CLI interface for JSON processor."""
    parser = argparse.ArgumentParser(
        description="Process JSON to compact, valid format using json5",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s input.json                    # Process file, print to stdout
  %(prog)s input.json -o output.json     # Process file, save to output
  %(prog)s --prompt                      # Interactive mode
  echo '{"a": 1,}' | %(prog)s            # Process from stdin
        """
    )

    parser.add_argument(
        'input_file', nargs='?',
        help='Input JSON file (or stdin if not provided)'
    )
    parser.add_argument(
        '-o', '--output',
        help='Output file (stdout if not provided)'
    )
    parser.add_argument(
        '--prompt', action='store_true',
        help='Interactive prompt mode'
    )
    parser.add_argument(
        '--pretty', action='store_true',
        help='Pretty print with indentation'
    )
    parser.add_argument(
        '--indent', type=int, default=2,
        help='Indentation spaces (default: 2)'
    )

    args = parser.parse_args()

    # Initialize processor
    processor = JsonProcessor(
        compact=not args.pretty,
        indent=args.indent if args.pretty else None
    )

    try:
        if args.prompt:
            # Interactive mode
            print("JSON Processor - Interactive Mode")
            print("Enter JSON (Ctrl+C to exit, empty line to process):")

            while True:
                try:
                    lines = []
                    while True:
                        line = input()
                        if not line.strip():
                            break
                        lines.append(line)

                    if not lines:
                        continue

                    input_json = '\n'.join(lines)
                    result = processor.process(input_json)
                    print("\nProcessed JSON:")
                    print(result)
                    print("\n" + "="*50 + "\n")

                except KeyboardInterrupt:
                    print("\nExiting...")
                    break
                except ValueError as e:
                    print(f"Error: {e}")
                    print()

        elif args.input_file:
            # File input mode
            result = processor.process_file(args.input_file, args.output)
            if not args.output:
                print(result)

        else:
            # Stdin mode
            input_content = sys.stdin.read()
            result = processor.process(input_content)

            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(result)
            else:
                print(result)

    except (FileNotFoundError, ValueError) as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)
    except KeyboardInterrupt:
        print("\nInterrupted", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()