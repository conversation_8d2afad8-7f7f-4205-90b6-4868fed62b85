
# GUIDELINES

- Discover optimal usage patterns before adding potential bloat to the codebase.
- Identify the single most critical aspect that would provide the greatest value while respecting existing project core.
- Ask yourself; what is the one, most important improvement that would bring true value?
- Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.
- Root things together fundamentally for natural simplicity and centralized interface(s).
- Simplicity, elegance, and fundamental connections.
- Concise single-line format only where needed.
- Self-explanatory code with minimal comments.
- Always aim for making targeted changes with highest (constructive and immediate) ROI.
- Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.
- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.
- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.
- Every move should bring clarity, simplicity, and natural elegance-and changes should fit together organically.
- Avoid bloating the codebase and not approaching it with respect
- Sustain perpetual generalization and maximal transferability by exclusively operationalizing solutions anchored in the invariant structural bottleneck, never confining to representational spectra.
- Always seek to address the underlying architectural problem that most elegantly solves the core issue.
- When identifying bottlenecks you must first understand the *reason* for why it's there (beore trying to "resolve" it), only then will you be able to determine whether the bottleneck can be *avoided* rather than merely "polishing it" for minimal gains. inefficiency can much more easily solved when you understand the *reasons*.
- Pursue solutions that address the issue in all conceivable scenarios without exceptions, focusing on maximal generalizability and eliminating context-specific fixations.

---

# GOAL

Write a json processor that takes any json input and leverage json5 to make it more compact, and to enforce valid json syntax.

Example input:
```
    {
      "uuid": "{DD6A6D64-D3F7-4962-A3F5-8BF8AD4098F1}",
      "name": "layout-0005-d--large",
      "type": "canvas",
      "info": {
        "ref-width": 3072,
        "ref-height": 1680,
        "zones": [
          {
            "X": 0,
            "Y": 1608,
            "width": 3072,
            "height": 72
          },
          {
            "X": 2481,
            "Y": 0,
            "width": 591,
            "height": 420
          },
          {
            "X": 1109,
            "Y": 0,
            "width": 805,
            "height": 1206
          },
          {
            "X": 0,
            "Y": 0,
            "width": 1109,
            "height": 420
          },
          {
            "X": 1914,
            "Y": 0,
            "width": 1158,
            "height": 420
          },
          {
            "X": 1109,
            "Y": 0,
            "width": 805,
            "height": 420
          },
          {
            "X": 1109,
            "Y": 0,
            "width": 1308,
            "height": 420
          },
          {
            "X": 0,
            "Y": 0,
            "width": 591,
            "height": 420
          },
          {
            "X": 0,
            "Y": 0,
            "width": 591,
            "height": 1206
          },
          {
            "X": 0,
            "Y": 0,
            "width": 591,
            "height": 1680
          },
          {
            "X": 0,
            "Y": 897,
            "width": 591,
            "height": 783
          },
          {
            "X": 0,
            "Y": 1206,
            "width": 591,
            "height": 474
          },
          {
            "X": 0,
            "Y": 0,
            "width": 1109,
            "height": 1206
          },
          {
            "X": 0,
            "Y": 0,
            "width": 1111,
            "height": 1680
          },
          {
            "X": 0,
            "Y": 897,
            "width": 1109,
            "height": 783
          },
          {
            "X": 0,
            "Y": 1206,
            "width": 1109,
            "height": 474
          },
          {
            "X": 2481,
            "Y": 0,
            "width": 591,
            "height": 1206
          },
          {
            "X": 2481,
            "Y": 0,
            "width": 591,
            "height": 1680
          },
          {
            "X": 2481,
            "Y": 897,
            "width": 591,
            "height": 783
          },
          {
            "X": 2481,
            "Y": 1206,
            "width": 591,
            "height": 474
          },
          {
            "X": 1914,
            "Y": 0,
            "width": 1158,
            "height": 1206
          },
          {
            "X": 1914,
            "Y": 0,
            "width": 1158,
            "height": 1680
          },
          {
            "X": 1914,
            "Y": 897,
            "width": 1158,
            "height": 783
          },
          {
            "X": 1914,
            "Y": 1206,
            "width": 1158,
            "height": 474
          },
          {
            "X": 1109,
            "Y": 0,
            "width": 1308,
            "height": 1206
          },
          {
            "X": 1109,
            "Y": 0,
            "width": 805,
            "height": 1680
          },
          {
            "X": 1109,
            "Y": 0,
            "width": 1300,
            "height": 1680
          },
          {
            "X": 1109,
            "Y": 897,
            "width": 1308,
            "height": 783
          },
          {
            "X": 1109,
            "Y": 897,
            "width": 805,
            "height": 783
          },
          {
            "X": 1109,
            "Y": 1206,
            "width": 1308,
            "height": 474
          },
          {
            "X": 1109,
            "Y": 1206,
            "width": 805,
            "height": 474
          },
          {
            "X": 0,
            "Y": 0,
            "width": 3072,
            "height": 397
          },
          {
            "X": 0,
            "Y": 1219,
            "width": 3072,
            "height": 461
          }
        ],
        "sensitivity-radius": 20
      }
    },
```

Should be compressed to this (while ensuring valid json syntax using json5):
```
    {
      "uuid": "{DD6A6D64-D3F7-4962-A3F5-8BF8AD4098F1}",
      "name": "layout-0005-d--large",
      "type": "canvas",
      "info": {
        "ref-width": 3072,
        "ref-height": 1680,
        "zones": [
          {"X": 0, "Y": 1608, "width": 3072, "height": 72},
          {"X": 2481, "Y": 0, "width": 591, "height": 420},
          {"X": 1109, "Y": 0, "width": 805, "height": 1206},
          {"X": 0, "Y": 0, "width": 1109, "height": 420},
          {"X": 1914, "Y": 0, "width": 1158, "height": 420},
          {"X": 1109, "Y": 0, "width": 805, "height": 420},
          {"X": 1109, "Y": 0, "width": 1308, "height": 420},
          {"X": 0, "Y": 0, "width": 591, "height": 420},
          {"X": 0, "Y": 0, "width": 591, "height": 1206},
          {"X": 0, "Y": 0, "width": 591, "height": 1680},
          {"X": 0, "Y": 897, "width": 591, "height": 783},
          {"X": 0, "Y": 1206, "width": 591, "height": 474},
          {"X": 0, "Y": 0, "width": 1109, "height": 1206},
          {"X": 0, "Y": 0, "width": 1111, "height": 1680},
          {"X": 0, "Y": 897, "width": 1109, "height": 783},
          {"X": 0, "Y": 1206, "width": 1109, "height": 474},
          {"X": 2481, "Y": 0, "width": 591, "height": 1206},
          {"X": 2481, "Y": 0, "width": 591, "height": 1680},
          {"X": 2481, "Y": 897, "width": 591, "height": 783},
          {"X": 2481, "Y": 1206, "width": 591, "height": 474},
          {"X": 1914, "Y": 0, "width": 1158, "height": 1206},
          {"X": 1914, "Y": 0, "width": 1158, "height": 1680},
          {"X": 1914, "Y": 897, "width": 1158, "height": 783},
          {"X": 1914, "Y": 1206, "width": 1158, "height": 474},
          {"X": 1109, "Y": 0, "width": 1308, "height": 1206},
          {"X": 1109, "Y": 0, "width": 805, "height": 1680},
          {"X": 1109, "Y": 0, "width": 1300, "height": 1680},
          {"X": 1109, "Y": 897, "width": 1308, "height": 783},
          {"X": 1109, "Y": 897, "width": 805, "height": 783},
          {"X": 1109, "Y": 1206, "width": 1308, "height": 474},
          {"X": 1109, "Y": 1206, "width": 805, "height": 474},
          {"X": 0, "Y": 0, "width": 3072, "height": 397},
          {"X": 0, "Y": 1219, "width": 3072, "height": 461}
        ],
        "sensitivity-radius": 20
      }
    },
```


---

the correct json syntax enforcement should also take care of non-json-trailing commas such as these:
```
    {
      "uuid": "{DD6A6D64-D3F7-4962-A3F5-8BF8AD4098F1}",
      "name": "layout-0005-d--large",
      "type": "canvas",
      "info": {
        "ref-width": 3072,
        "ref-height": 1680,
        "zones": [
          {"X": 0, "Y": 1608, "width": 3072, "height": 72},
          {"X": 2481, "Y": 0, "width": 591, "height": 420},
          {"X": 1109, "Y": 0, "width": 805, "height": 1206},
          {"X": 0, "Y": 0, "width": 1109, "height": 420},
          {"X": 1914, "Y": 0, "width": 1158, "height": 420},
          {"X": 1109, "Y": 0, "width": 805, "height": 420},
          {"X": 1109, "Y": 0, "width": 1308, "height": 420},
          {"X": 0, "Y": 0, "width": 591, "height": 420},
          {"X": 0, "Y": 0, "width": 591, "height": 1206},
          {"X": 0, "Y": 0, "width": 591, "height": 1680},
          {"X": 0, "Y": 897, "width": 591, "height": 783},
          {"X": 0, "Y": 1206, "width": 591, "height": 474},
          {"X": 0, "Y": 0, "width": 1109, "height": 1206},
          {"X": 0, "Y": 0, "width": 1111, "height": 1680},
          {"X": 0, "Y": 897, "width": 1109, "height": 783},
          {"X": 0, "Y": 1206, "width": 1109, "height": 474},
          {"X": 2481, "Y": 0, "width": 591, "height": 1206},
          {"X": 2481, "Y": 0, "width": 591, "height": 1680},
          {"X": 2481, "Y": 897, "width": 591, "height": 783},
          {"X": 2481, "Y": 1206, "width": 591, "height": 474},
          {"X": 1914, "Y": 0, "width": 1158, "height": 1206},
          {"X": 1914, "Y": 0, "width": 1158, "height": 1680},
          {"X": 1914, "Y": 897, "width": 1158, "height": 783},
          {"X": 1914, "Y": 1206, "width": 1158, "height": 474},
          {"X": 1109, "Y": 0, "width": 1308, "height": 1206},
          {"X": 1109, "Y": 0, "width": 805, "height": 1680},
          {"X": 1109, "Y": 0, "width": 1308, "height": 1680},
          {"X": 1109, "Y": 897, "width": 1308, "height": 783},
          {"X": 1109, "Y": 897, "width": 805, "height": 783},
          {"X": 1109, "Y": 1206, "width": 1308, "height": 474},
          {"X": 1109, "Y": 1206, "width": 805, "height": 474},
          {"X": 0, "Y": 0, "width": 3072, "height": 397},
          {"X": 0, "Y": 1219, "width": 3072, "height": 461},
        ],
        "sensitivity-radius": 20,
      },
    },
```
