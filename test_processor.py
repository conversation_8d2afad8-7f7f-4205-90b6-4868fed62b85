#!/usr/bin/env python3
"""
Test script to demonstrate the JSON processor functionality.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from json_processor import JsonProcessor

def test_basic_functionality():
    """Test basic JSON processing functionality."""
    print("=== Testing Basic JSON Processing ===")
    
    processor = JsonProcessor()
    
    # Test 1: Valid JSON compaction
    test_json = '''
    {
      "name": "test",
      "values": [
        1,
        2,
        3
      ],
      "nested": {
        "key": "value"
      }
    }
    '''
    
    result = processor.process(test_json)
    print("Input (formatted):")
    print(test_json)
    print("\nOutput (compact):")
    print(result)
    print()

def test_trailing_commas():
    """Test handling of trailing commas (JSON5 features)."""
    print("=== Testing Trailing Comma Handling ===")
    
    processor = JsonProcessor()
    
    # Test with trailing commas
    malformed_json = '''
    {
      "name": "test",
      "values": [
        1,
        2,
        3,
      ],
      "nested": {
        "key": "value",
      },
    }
    '''
    
    result = processor.process(malformed_json)
    print("Input (with trailing commas):")
    print(malformed_json)
    print("\nOutput (valid JSON):")
    print(result)
    print()

def test_pretty_print():
    """Test pretty printing functionality."""
    print("=== Testing Pretty Print ===")
    
    processor = JsonProcessor(compact=False, indent=2)
    
    compact_json = '{"name":"test","values":[1,2,3],"nested":{"key":"value"}}'
    
    result = processor.process(compact_json)
    print("Input (compact):")
    print(compact_json)
    print("\nOutput (pretty):")
    print(result)
    print()

def test_real_world_example():
    """Test with the real-world example from GOAL.md."""
    print("=== Testing Real-World Example ===")
    
    processor = JsonProcessor()
    
    # Use the malformed JSON from the test file
    try:
        with open('test_malformed.json', 'r') as f:
            malformed_content = f.read()
        
        result = processor.process(malformed_content)
        
        print("Successfully processed malformed JSON with trailing commas!")
        print(f"Original size: {len(malformed_content)} characters")
        print(f"Compact size: {len(result)} characters")
        print(f"Size reduction: {((len(malformed_content) - len(result)) / len(malformed_content) * 100):.1f}%")
        
        # Show first 200 characters of result
        print(f"\nFirst 200 chars of result: {result[:200]}...")
        
    except FileNotFoundError:
        print("test_malformed.json not found, skipping real-world test")
    print()

if __name__ == "__main__":
    print("JSON Processor Test Suite")
    print("=" * 50)
    
    test_basic_functionality()
    test_trailing_commas()
    test_pretty_print()
    test_real_world_example()
    
    print("All tests completed successfully! ✅")
